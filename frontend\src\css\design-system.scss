// Design System CSS Variables
// Based on dark theme aesthetic with customizable tokens

:root {
  // ===== COLOR SYSTEM =====

  // Primary Brand Colors
  --color-primary: #4a9eff;
  --color-primary-hover: #3d8ae6;
  --color-primary-active: #2e7bd4;
  --color-primary-light: rgba(74, 158, 255, 0.1);

  // Background Colors
  --color-bg-primary: #1a1d29;
  --color-bg-secondary: #252a3a;
  --color-bg-tertiary: #2f3447;
  --color-bg-elevated: #353b4f;
  --color-bg-overlay: rgba(26, 29, 41, 0.95);

  // Surface Colors
  --color-surface-primary: #252a3a;
  --color-surface-secondary: #2f3447;
  --color-surface-hover: #353b4f;
  --color-surface-active: #3d4357;

  // Text Colors
  --color-text-primary: #ffffff;
  --color-text-secondary: #b8c1d9;
  --color-text-muted: #8892a6;
  --color-text-disabled: #5a6478;
  --color-text-inverse: #1a1d29;

  // Border Colors
  --color-border-primary: #3d4357;
  --color-border-secondary: #2f3447;
  --color-border-focus: var(--color-primary);
  --color-border-error: var(--color-negative);

  // Market-specific Colors (Yes/No/Order)
  --color-yes-primary: #27ae60;
  --color-yes-hover: #229954;
  --color-yes-background: rgba(39, 174, 96, 0.1);
  --color-yes-light: rgba(39, 174, 96, 0.05);

  --color-no-primary: #e74c3c;
  --color-no-hover: #d63031;
  --color-no-background: rgba(231, 76, 60, 0.1);
  --color-no-light: rgba(231, 76, 60, 0.05);

  --color-order-primary: #f39c12;
  --color-order-hover: #e67e22;
  --color-order-background: rgba(243, 156, 18, 0.1);
  --color-order-light: rgba(243, 156, 18, 0.05);

  // Status Colors
  --color-positive: #27ae60;
  --color-negative: #e74c3c;
  --color-warning: #f39c12;
  --color-info: #3498db;

  // ===== TYPOGRAPHY SYSTEM =====

  // Font Families
  --font-family-primary: 'OpenSauceSans', 'Roboto', -apple-system, 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  // Font Sizes
  --font-size-xs: 0.75rem;    // 12px
  --font-size-sm: 0.875rem;   // 14px
  --font-size-base: 1rem;     // 16px
  --font-size-lg: 1.125rem;   // 18px
  --font-size-xl: 1.25rem;    // 20px
  --font-size-2xl: 1.5rem;    // 24px
  --font-size-3xl: 1.875rem;  // 30px
  --font-size-4xl: 2.25rem;   // 36px

  // Font Weights
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  // Line Heights
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  // ===== SPACING SYSTEM =====

  --spacing-xs: 0.25rem;   // 4px
  --spacing-sm: 0.5rem;    // 8px
  --spacing-md: 0.75rem;   // 12px
  --spacing-lg: 1rem;      // 16px
  --spacing-xl: 1.5rem;    // 24px
  --spacing-2xl: 2rem;     // 32px
  --spacing-3xl: 3rem;     // 48px
  --spacing-4xl: 4rem;     // 64px

  // ===== BORDER RADIUS SYSTEM =====

  --radius-none: 0;
  --radius-sm: 0.25rem;    // 4px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-full: 9999px;

  // ===== SHADOW SYSTEM =====

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);

  // ===== TRANSITION SYSTEM =====

  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  // ===== Z-INDEX SYSTEM =====

  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// ===== SCROLLBAR STYLING =====

// Custom scrollbars for dark theme
* {
  scrollbar-width: auto;
  scrollbar-color: var(--color-border-primary) var(--color-surface-secondary);
}

// Webkit scrollbars (Chrome, Safari, Edge)
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: var(--color-surface-secondary);
  border-radius: var(--radius-sm);
}

*::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-surface-secondary);
  transition: background-color var(--transition-fast);
}

*::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}

*::-webkit-scrollbar-thumb:active {
  background: var(--color-text-secondary);
}

*::-webkit-scrollbar-corner {
  background: var(--color-surface-secondary);
}

// ===== UTILITY CLASSES =====

// Text Utilities
.text-primary { color: var(--color-text-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-muted { color: var(--color-text-muted) !important; }
.text-disabled { color: var(--color-text-disabled) !important; }

.text-yes { color: var(--color-yes-primary) !important; }
.text-no { color: var(--color-no-primary) !important; }
.text-order { color: var(--color-order-primary) !important; }

.text-positive { color: var(--color-positive) !important; }
.text-negative { color: var(--color-negative) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-info { color: var(--color-info) !important; }

// Font Weight Utilities
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }
.font-extrabold { font-weight: var(--font-weight-extrabold) !important; }

// Font Size Utilities
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }

// Background Utilities
.bg-primary { background-color: var(--color-bg-primary) !important; }
.bg-secondary { background-color: var(--color-bg-secondary) !important; }
.bg-tertiary { background-color: var(--color-bg-tertiary) !important; }
.bg-elevated { background-color: var(--color-bg-elevated) !important; }

.bg-surface { background-color: var(--color-surface-primary) !important; }
.bg-surface-secondary { background-color: var(--color-surface-secondary) !important; }

.bg-yes { background-color: var(--color-yes-background) !important; }
.bg-no { background-color: var(--color-no-background) !important; }
.bg-order { background-color: var(--color-order-background) !important; }

// Border Utilities
.border-primary { border-color: var(--color-border-primary) !important; }
.border-secondary { border-color: var(--color-border-secondary) !important; }
.border-focus { border-color: var(--color-border-focus) !important; }

// Shadow Utilities
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-inner { box-shadow: var(--shadow-inner) !important; }
.shadow-none { box-shadow: none !important; }

// Border Radius Utilities
.rounded-none { border-radius: var(--radius-none) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

// ===== COMPONENT DESIGN TOKENS =====

// Button System
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
    box-shadow: var(--shadow-md);
  }

  &:active {
    background-color: var(--color-primary-active);
    border-color: var(--color-primary-active);
  }

  &:disabled {
    background-color: var(--color-text-disabled);
    border-color: var(--color-text-disabled);
    color: var(--color-text-muted);
    cursor: not-allowed;
    box-shadow: none;
  }
}

.btn-secondary {
  background-color: var(--color-surface-primary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-surface-hover);
    border-color: var(--color-border-focus);
    color: var(--color-text-primary);
  }
}

.btn-yes {
  background-color: var(--color-yes-background);
  color: var(--color-yes-primary);
  border: 1px solid var(--color-yes-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-yes-primary);
    color: var(--color-text-primary);
  }

  &.btn-selected {
    background-color: var(--color-yes-primary);
    color: var(--color-text-primary);
  }
}

.btn-no {
  background-color: var(--color-no-background);
  color: var(--color-no-primary);
  border: 1px solid var(--color-no-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-no-primary);
    color: var(--color-text-primary);
  }

  &.btn-selected {
    background-color: var(--color-no-primary);
    color: var(--color-text-primary);
  }
}

.btn-order {
  background-color: var(--color-order-background);
  color: var(--color-order-primary);
  border: 1px solid var(--color-order-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-order-primary);
    color: var(--color-text-primary);
  }

  &.btn-selected {
    background-color: var(--color-order-primary);
    color: var(--color-text-primary);
  }
}

// Card System
.card {
  background-color: var(--color-surface-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);

  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--color-border-primary);
  }
}

.card-elevated {
  background-color: var(--color-bg-elevated);
  box-shadow: var(--shadow-md);

  &:hover {
    box-shadow: var(--shadow-lg);
  }
}

// Input System
.input-field {
  background-color: var(--color-surface-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-lg);
  color: var(--color-text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  transition: all var(--transition-fast);

  &:focus {
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 3px var(--color-primary-light);
    outline: none;
  }

  &::placeholder {
    color: var(--color-text-muted);
  }
}

// Header System
.header-primary {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.header-secondary {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.header-tertiary {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

// ===== TABLE SYSTEM =====

// Sticky header table styling
.table-sticky-header {
  :deep(table) {
    thead {
      th {
        position: sticky;
        z-index: var(--z-sticky);
        background-color: var(--color-surface-primary);
        color: var(--color-text-primary);
        border-bottom: 1px solid var(--color-border-secondary);
      }
      tr:first-child th {
        top: 0;
      }
    }
    tbody {
      scroll-margin-top: 28px;

      tr {
        background-color: var(--color-surface-primary);
        color: var(--color-text-primary);
        transition: background-color var(--transition-fast);

        &:hover {
          background-color: var(--color-surface-hover);
        }
      }

      td {
        border-bottom: 1px solid var(--color-border-secondary);
      }
    }
  }
}

// Scrollable table container
.table-scrollable {
  max-height: 300px;
  overflow-y: auto;
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--color-surface-primary);

  table {
    background-color: var(--color-surface-primary);
    border-radius: var(--radius-md);
    overflow: hidden;
  }
}

// Table cell padding variations
.table-compact {
  tbody td {
    padding: 3px 8px;
  }
}

// Button styling for table actions
.btn-table-action {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  transition: color var(--transition-fast);
  padding-left: 6px;
  padding-right: 6px;

  &:hover {
    color: var(--color-text-primary);
  }
}

// ===== CONTAINER SYSTEM =====

// Main container with border and padding
.container-main {
  border-top: 2px dashed var(--color-border-secondary);
  background-color: var(--color-surface-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

// Text size utility for small text
.text-small {
  font-size: var(--font-size-xs);
}

// ===== LAYOUT UTILITIES =====

// Flexbox utilities
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

// Position utilities
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

// Overflow utilities
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

// Width and height utilities
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-0 {
  min-height: 0;
}

// Spacing utilities
.p-0 { padding: 0 !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

.m-0 { margin: 0 !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

// Margin top/bottom
.mt-0 { margin-top: 0 !important; }
.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }

// Margin left/right
.ml-0 { margin-left: 0 !important; }
.ml-xs { margin-left: var(--spacing-xs) !important; }
.ml-sm { margin-left: var(--spacing-sm) !important; }
.ml-md { margin-left: var(--spacing-md) !important; }
.ml-lg { margin-left: var(--spacing-lg) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-xs { margin-right: var(--spacing-xs) !important; }
.mr-sm { margin-right: var(--spacing-sm) !important; }
.mr-md { margin-right: var(--spacing-md) !important; }
.mr-lg { margin-right: var(--spacing-lg) !important; }
