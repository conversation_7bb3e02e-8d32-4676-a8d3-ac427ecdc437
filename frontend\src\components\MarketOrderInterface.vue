<template>
  <div class="row container-connected full-width">
    <!-- Limit Order Form -->
    <div class="col-12 col-md-5">
      <OrderInterface
        :market-data="props.marketData"
        :order-data="props.orderData"
        :cost="estimatedCost"
        v-model:is-buy-selected="selectedBuy"
        v-model:is-yes-selected="selectedYes"
        v-model:price="priceInput"
        v-model:shares="sharesInput"
        @place-order="onPlaceOrder"
        @cancel-orders="onClickCancelOrders"
      />
    </div>

    <div class="col-12 col-md-7">
      <!-- Order Book -->
      <q-table v-if="!showHistory" dense hide-bottom row-hover wrap-cells
        ref = "elmBook"
        :rows="bookRowData"
        :pagination="paginationNone"
        row-key="price"
        class="table-sticky-header table-scrollable table-compact tbl-book"
      >
        <template #header="{}">
          <tr>
            <th style="width: 80px; min-width: 80px">
              <q-btn v-if="props.historyData" flat dense no-caps class="btn-table-action" title="Trade history for market"
                @click="onClickHistory"
              >
                <q-icon name="history" />
              </q-btn>
            </th>
            <th style="width: 23.33%" class="text-right">Price</th>
            <th style="width: 43.33%" class="text-right">Shares</th>
            <th style="width: 33.33%" class="text-right">Total</th>
          </tr>
        </template>
        <template #body="{ row }: { row: BookRowVM }">
          <tr v-if="row.price !== 0 || (!expandMarketActivity || marketActivity)"
           @click="onClickBookRow(row)" :key="row.price"
           class="cursor-pointer text-right"
           :class="!row.shares && row.price ? 'row-volume' : ''"
          >
            <!-- actions -->
            <td class="cell-action">
              <!-- cancel order -->
              <q-btn v-if="row.orderInfo" no-caps flat
                icon="cancel" class="text-no" size="xs"
                :title="`Cancel ${row.orderInfo.orders.length} order(s) ${row.orderInfo.totalSharesMatched}/${row.orderInfo.totalShares} filled`"
                @click.stop="onClickCancelOrders(row.orderInfo.orders)"
              />
              <!-- expand market activity -->
              <q-btn v-if="!row.price" no-caps flat
                :icon="expandMarketActivity ? 'close_fullscreen'  : 'analytics'" class="" size="xs"
                :title="`Show market trade activity`"
                @click.stop="onClickExpandMarketActivity"
              />
              <!-- vol time -->
              <span v-if="row.shareVol" class="text-vol text-small">
                {{ `${formatTimeAgoShort(row.shareVolTime, 0)} ` }}
              </span>
            </td>
            <!-- price -->
            <td :class="row.shares ? (row.isBid ? 'text-yes' : 'text-no') : 'text-vol'">
              {{ row.price ? formatCents(row.price) : '' }}
            </td>
            <!-- shares -->
            <td>
              <span v-if="row.shareVol" class="text-vol">{{ `${formatDecimal(row.shareVol, 0, true)} ` }}</span>
              <span v-if="row.shares">{{ formatDecimal(row.shares, 0, true) }}</span>
              <b v-if="!row.price">{{ `Spread: ${formatCents(row.total)}` }}</b>
            </td>
            <!-- total -->
            <td>
              {{ row.price && row.shares ? formatCurrency(row.total, 0) : '' }}
            </td>
          </tr>
          <!-- Market history display -->
          <tr v-else :key="0">
            <td colspan="4">
              <div class="flex justify-center">
                <q-spinner-grid color="primary" />
              </div>
            </td>
          </tr>
        </template>
      </q-table>
      <!-- History -->
      <q-table v-else dense hide-bottom row-hover wrap-cells
        virtual-scroll virtual-scroll-item-size="28.5" virtual-scroll-sticky-size-start="28.5"
        :rows="props.historyData!"
        :pagination="paginationNone"
        row-key="price"
        class="table-sticky-header table-scrollable tbl-history"
      >
        <template #header="{ }">
          <tr>
            <th style="width: 40px">
              <q-btn flat dense no-caps class="btn-table-action" title="Trade history for market"
                @click="onClickHistory"
              >
                <q-icon name="arrow_back" />
              </q-btn>
            </th>
            <th style="width: 40px">Side</th>
            <th style="width: 40px"></th>
            <th style="width: 50px">Price</th>
            <th style="width: 50px">Shares</th>
            <th style="width: 33.33%">Total</th>
          </tr>
        </template>
        <template #body="{ row }: { row: PolyDataHistory }">
          <tr class="text-right" :key="row.uniqueId">
            <!-- info (time) -->
            <td style="width: 40px" class="text-muted text-small text-left">
              {{ formatTimeAgoShort(row.time, 0) }}
            </td>
            <!-- side (buy/sell) -->
            <td :class="{'bg-yes': row.side === HistorySide.Buy, 'bg-no': row.side === HistorySide.Sell}">
              <b>{{ historySideToString(row.side) }}</b>
            </td>
            <!-- outcome -->
            <td :class="{'text-yes': row.assetId == props.marketData.assetIdA, 'text-no': row.assetId == props.marketData.assetIdB}">
              {{ props.marketData.lookupAssetName(row.assetId) }}
            </td>
            <!-- price -->
            <td>
              {{ formatCents(row.price) }}
            </td>
            <!-- shares -->
            <td>
              {{ formatDecimal(row.shares * (row.side === HistorySide.Sell ? -1 : 1), 2, true) }}
            </td>
            <!-- total -->
            <td>
              {{ formatCurrency(row.price * row.shares * (row.side === HistorySide.Buy ? -1 : 1), 0) }}
            </td>
          </tr>
        </template>
      </q-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, UnwrapRef } from 'vue';
import { QBtn, QBtnGroup, QInput, QTable } from 'quasar';
import { formatCurrency, formatCents, formatDecimal, formatTimeAgoShort, debounce } from 'src/utils';
import { HistorySide, historySideToString, PolyDataBookOrder, PolyDataHistory, PolyDataMarket, PolyDataOpenOrder, PolyDataPosition } from '@shared/api-dataclasses-shared';
import { useEventStore } from 'src/stores/event-store';
import { useNotifier } from 'src/notifier';
import OrderInterface from 'src/components/OrderInterface.vue';
import { useOrderStore } from 'src/stores/order-store';
import { useMarketActivityStore } from 'src/stores/market-activity-store';
import MarketActivityModel from 'src/models/market-activity-model';

const props = defineProps<{
  eventId: string,
  marketData: PolyDataMarket,
  orderData?: PolyDataOpenOrder[],
  historyData?: PolyDataHistory[],
}>();

declare interface BookRowVM {
  price: number,
  shares: number,
  total: number,
  isBid: boolean,
  orderInfo: { orders: PolyDataOpenOrder[], totalSharesMatched: number, totalShares: number } | null,
  shareVol: number,
  shareVolTime: Date,
}

const notifier = useNotifier();
const eventStore = useEventStore(); //Only used for eventStore.refreshMarketBook in the odd event of a bid below an ask (nearly never happens anymore)
const orderStore = useOrderStore();
const activityStore = useMarketActivityStore();
const marketActivityInterval = 1800000;
let isFirstBookUpdate = true;

//Refs

const elmBook = ref<QTable | null>(null);
const selectedYes = ref(true);
const selectedBuy = ref(true);
const priceInput = ref('');
const sharesInput = ref('');
// Basic pagination with no sorting
const paginationNone = ref({
  page: 1,
  rowsPerPage: 0, // show all in one page
  sortBy: null,
  descending: false
});
const showHistory = ref(false);
const expandMarketActivity = ref(false);
const marketActivity = ref<MarketActivityModel[] | null>(null);

//Computed

const lookupOrdersByPrice = computed(() => {
  const lookup: Record<number, { orders: PolyDataOpenOrder[], totalSharesMatched: number, totalShares: number } | null> = {};
  for (const order of props.orderData || []) {
    const lookupEntry = lookup[order.price] || (lookup[order.price] = { orders: [], totalSharesMatched: 0, totalShares: 0 });
    lookupEntry.orders.push(order);
    lookupEntry.totalSharesMatched += order.sharesMatched;
    lookupEntry.totalShares += order.sharesTotal;
  }

  return lookup;
});

const marketVolumeByPrice = computed(() => {
  if (!marketActivity.value)
    return {};

  const market = props.marketData;
  let flipAssetId;
  let bestBid, bestAsk;
  if (selectedYes.value) {
    flipAssetId = market.assetIdB;
    bestBid = market.bestBidA;
    bestAsk = market.bestAskA;
  }
  else {
    flipAssetId = market.assetIdA;
    bestBid = market.bestBidB;
    bestAsk = market.bestAskB;
  }

  const volByPrice: Record<string, { shares: number, mostRecentTime: Date }> = {};
  //Normalize activity data (1-price to flip sides)
  for (const activity of marketActivity.value) {
    let price = activity.price;
    const shares = activity.shares;

    if (flipAssetId === activity.asset_id) {
      price = 1 - activity.price;
    }

    price = market.roundToTickSize(price);

    //Push data to lookup
    const volByPriceVal = volByPrice[price] ?? (volByPrice[price] = { shares: 0, mostRecentTime: new Date(activity.time) });
    volByPriceVal.shares += shares;
    const timeDate = new Date(activity.time);
    if (timeDate > volByPriceVal.mostRecentTime) {
      volByPriceVal.mostRecentTime = timeDate;
    }
  }

  return volByPrice;
});

//TODO: Doing this every book update may be a bit intensive. Optimize later.
const bookRowData = computed((): BookRowVM[] => {
  const orderBook = selectedYes.value ? props.marketData.bookLookupA : props.marketData.bookLookupB;

  const ordersByPrice = lookupOrdersByPrice.value;
  const volByPrice = marketVolumeByPrice.value;
  let rowData: BookRowVM[] = [];
  let highestBid = 0;
  for (const priceStr in orderBook) {
    const bookData = orderBook[priceStr];
    const volData = volByPrice[priceStr];
    rowData.push({
      price: bookData.price,
      shares: bookData.shares,
      total: 0,
      isBid: bookData.isBid,
      orderInfo: ordersByPrice[bookData.price],
      shareVol: volData?.shares ?? 0,
      shareVolTime: volData?.mostRecentTime ?? new Date(0)
    });

    if (bookData.isBid && bookData.price > highestBid) {
      highestBid = bookData.price;
    }
  }

  if (rowData.length === 0) {
    return rowData;
  }

  //Add any price levels from volume/activity data that aren't in the order book
  for (const priceStr in volByPrice) {
    if (!orderBook[priceStr]) {
      const volData = volByPrice[priceStr];
      rowData.push({
        price: Number(priceStr),
        shares: 0,
        total: 0,
        isBid: false,
        orderInfo: null,
        shareVol: volData.shares,
        shareVolTime: volData.mostRecentTime
      });
    }
  }
  //Sort by price descending (asks front of array, bids back)
  rowData.sort((a, b) => b.price - a.price);
  //Find index of highest bid
  let highestBidInd = rowData.findIndex((row) => row.price == highestBid);
  if (highestBidInd === -1) {
    highestBidInd = rowData.length;
  }
  //Calculate totals for asks
  let total = 0;
  for (let i = highestBidInd - 1; i > -1; i--) {
    const row = rowData[i];
    total += row.shares * row.price;
    row.total = total;
  }
  //Calculate totals for bids
  total = 0;
  for (let i = highestBidInd; i < rowData.length; i++) {
    const row = rowData[i];
    total += row.shares * row.price;
    row.total = total;
  }
  //Add spread row
  rowData.splice(highestBidInd, 0, {
    price: 0,
    shares: 0,
    total: (rowData[highestBidInd - 1]?.price || 1) - highestBid,
    isBid: false,
    orderInfo: null,
    shareVol: 0,
    shareVolTime: new Date(0)
  });

  //DEBUG: check for any asks below highest bid
  let dbg;
  if (dbg = rowData.find((row) => !row.isBid && row.shares !== 0 && row.price < highestBid)) {
    console.warn('WARNING: ask below highest bid!', highestBid, dbg, rowData);
    //console.warn('Last dbg: ', user.getLastDbgMsg(), user.getLastDbgObj());

    eventStore.refreshMarketBook(props.marketData);
  }

  return rowData;
});

const spreadRowInd = computed(() => {
  return bookRowData.value.findIndex((row) => row.price === 0);
});

const estimatedCost = computed(() => {
  let price = Number(priceInput.value);
  let shares = Number(sharesInput.value);
  if (!price || !shares)
    return 0;
  let startInd;
  let endInd;
  let dir;
  if (!selectedBuy.value) {
    startInd = spreadRowInd.value + 1;
    endInd = bookRowData.value.length;
    dir = 1;
  }
  else {
    startInd = spreadRowInd.value - 1;
    endInd = -1;
    dir = -1;
  }

  const book = bookRowData.value;
  const rowStart = book[startInd];
  price = price / 100;
  //console.log("isBuy: ", selectedBuy.value, "spread: ", book[spreadRowInd.value], "start", rowStart);
  if (!rowStart || (selectedBuy.value && price < rowStart.price) || (!selectedBuy.value && price > rowStart.price)) {
    //console.log('Price out of range, price: ', price, shares, price * shares);
    return price * shares;
  }
  //Get total from input price exhausting input shares
  let totalCost = 0;
  let sharesLeft = shares;
  for (let i = startInd; i != endInd; i+=dir) {
    const row = book[i];
    if (row.shares === 0)
      continue;
    //Terminate if we've gone past the input price
    if (dir === 1 ? price > row.price : price < row.price) {
      totalCost += sharesLeft * price;
      break;
    }
    const sharesToUse = Math.min(sharesLeft, row.shares);
    totalCost += sharesToUse * row.price;
    sharesLeft -= sharesToUse;
    //console.log("adding", row.price, sharesToUse, row.price * sharesToUse);
    if (sharesLeft <= 0) {
      break;
    }
  }

  //console.log("--total: ", totalCost, "--")
  return totalCost;
});

//Watchers

// watch(selectedYes, () => {
//   scrollBookToCenter();
// });

let lastSelectedYes = selectedYes.value;
watch(bookRowData, async (newBook, oldBook) => {
  let bookChanged = false;
  if (selectedYes.value != lastSelectedYes) {
    lastSelectedYes = selectedYes.value;
    bookChanged = true;
  }

  if (isFirstBookUpdate && newBook.length > 0) {
    isFirstBookUpdate = false;
    bookChanged = true;
  }

  if (bookChanged) {
    scrollBookToCenter();
  }
  else {
    //Track book center row and prevent it from moving out of view
    const rowInfo = measureCenterRow(oldBook);
    if (!rowInfo)
      return;
    await nextTick();
    scrollBookToTrackedRow(rowInfo.row.price, rowInfo.rowOffsetFromScrollElm);
  }
});

//Functions

function onClickBookRow(rowData: BookRowVM) {
  let totalShares = 99999999;
  let price = rowData.price;

  //Clicked spread row
  if (price === 0) {
    //Clicked spread row, split price between bid and ask
    let bestBid, bestAsk = 0;
    if (selectedYes.value) {
      bestBid = props.marketData.bestBidA;
      bestAsk = props.marketData.bestAskA;
    }
    else {
      bestBid = props.marketData.bestBidB;
      bestAsk = props.marketData.bestAskB;
    }

    price = (bestBid + bestAsk) / 2;

    if (props.marketData.tickSize == '0.01') {
      if (selectedBuy.value) {
        price = Math.floor(Number((price * 100).toFixed(0))) / 100; //toFixed stuff fixes 7.0000000001 type issues
      }
      else {
        price = Math.ceil(Number((price * 100).toFixed(0))) / 100;
      }
    }

    if (selectedBuy.value) {
      totalShares = props.marketData.positionA?.shares ?? 0;
    }
    else {
      totalShares = props.marketData.positionB?.shares ?? 0;
    }
  }
  //Clicked volume display row
  else if (rowData.shares === 0) {
    //TODO: Determine buy/sell and which real order rows lay above/below this
    totalShares = 0;
    price = rowData.price;
  }
  //Clicked regular row
  else {
    //Get spread row
    const spreadRow = bookRowData.value.findIndex((row) => row.price === 0);
    if (spreadRow === -1)
      throw new Error(`Spread row not found in order book data ${props.marketData.shortName}`);
    const rowIndex = bookRowData.value.findIndex((row) => row.price === rowData.price);
    //Calc total shares from spread row to clicked row
    const dir = rowData.isBid ? 1 : -1;
    totalShares = 0;
    for (let i = spreadRow + dir; i !== rowIndex + dir; i += dir) {
      totalShares += bookRowData.value[i].shares;
    }
    //Set buy/sell
    selectedBuy.value = !rowData.isBid;

    if (!selectedBuy.value) {
      if (selectedYes.value && props.marketData.positionA) {
        totalShares = Math.min(props.marketData.positionA.shares, totalShares);
      }
      else if (!selectedYes.value && props.marketData.positionB) {
        totalShares = Math.min(props.marketData.positionB.shares, totalShares);
      }
      else {
        //No position, no shares to sell
        totalShares = 0;
      }
    }
    else {
    }
  }

  //Set price and shares
  priceInput.value = formatDecimal(price * 100, 1, true);
  sharesInput.value = formatDecimal(totalShares, 2, false);
}

async function onPlaceOrder(data: { price: number, shares: number, isBuy: boolean, isYes: boolean }) {
  console.log('Submitting:', {
    contractChoice: data.isYes ? 'YES' : 'NO',
    orderType: data.isBuy ? 'BUY' : 'SELL',
    priceInCents: data.price,
    shares: data.shares,
    price: data.price,
    tickSize: props.marketData.tickSize
  });

  //Send order to API
  const res = await orderStore.placeOrder(data.isYes ? props.marketData.assetIdA : props.marketData.assetIdB, data.price, data.shares, data.isBuy, props.marketData.tickSize);
  console.log('Order response:', res);

  //Handle success/fail
  if (res.success) {
    notifier.success(`Order ${res.status}! ${data.isBuy ? 'BUY' : 'SELL'} ${data.shares} @ ${formatCents(data.price)}`);
  }
  else {
    notifier.error(`Order failed: ${res.errorMsg}`);
  }
}

async function onClickCancelOrders(orders: PolyDataOpenOrder[]) {
  console.log('Cancelling orders:', orders);

  const res = await orderStore.cancelOrder(orders.map((order) => order.id));

  if (res.canceled.length) {
    notifier.success(`${orders.length} ${orders[0].isBuy ? 'BUY' : 'SELL'} Order(s) @ ${formatCents(orders[0].price)} CANCELED`);
  }
  if (res.not_canceled.length) {
    const notCanceled = Object.values(res.not_canceled);
    notifier.error(`Cancel failed for ${notCanceled.length} order(s): ${notCanceled.join(', ')}`);
  }
}

async function onClickHistory() {
  showHistory.value = !showHistory.value;
  scrollBookToCenter();
}

async function onClickExpandMarketActivity() {
  expandMarketActivity.value = !expandMarketActivity.value;

  if (expandMarketActivity.value) {
    activityStore.clearMarketActivity(props.marketData.conditionId);
    await activityStore.fetchRecentActivity([props.marketData.conditionId], marketActivityInterval);
    marketActivity.value = activityStore.activityByCondId[props.marketData.conditionId] ?? [];
  }
  else {
    marketActivity.value = null;
  }
}

async function scrollBookToCenter() {
  await nextTick();
  if (!elmBook.value) return;
  const centerInd = bookRowData.value.findIndex((row) => row.price === 0);
  if (centerInd === -1) return;
  const elmTable = elmBook.value.$el.querySelector('.q-table');
  const elmScroll = elmBook.value.$el.querySelector('.scroll');
  const elmTrCenterRect = elmScroll.querySelectorAll('tbody tr')[centerInd].getBoundingClientRect();
  elmScroll.scrollTop = (elmTrCenterRect.top - elmTable.getBoundingClientRect().top) + (elmTrCenterRect.height/2) - (elmScroll.getBoundingClientRect().height/2);
}

function measureCenterRow(oldBook: { price: number }[]) {
  const elmScroll = elmBook.value?.$el.querySelector('.scroll');
  const rowHeight = 28; //TODO: Get this from first row
  const rowHeaderHeight = 32;
  if (!elmScroll)
    return null;

  // Convert that pixel position to a "row index" (assuming uniform rowHeight)
  const scrollRect = elmScroll.getBoundingClientRect();
  const scrollCenterDist = elmScroll.scrollTop + scrollRect.height/2;
  const centerRowInd = Math.floor((scrollCenterDist - rowHeaderHeight) / rowHeight);

  // Get the actual row data & store the key
  const row = oldBook[centerRowInd];
  if (!row)
    return null;

  // Find the actual DOM element for that row
  // We'll assume each <tr> corresponds in order to the array
  const elmCenterTr = elmScroll.querySelectorAll('tbody tr')[centerRowInd];
  if (!elmCenterTr)
    return null;

  // Measure how far that row's center is from the container's center (on screen)
  const rowRect = elmCenterTr.getBoundingClientRect();

  return { row: row, rowOffsetFromScrollElm: rowRect.top - scrollRect.top };
}

async function scrollBookToTrackedRow(rowPrice: number, rowOffsetFromScrollElm: number) {
  const elmTable = elmBook.value!.$el.querySelector('.q-table');
  const elmScroll = elmBook.value!.$el.querySelector('.scroll');
  if (!elmScroll)
    return;

  // Find the row in the new data
  const centerRowInd = bookRowData.value.findIndex(row => row.price === rowPrice);
  if (centerRowInd === -1) {
    // The row may have been removed
    //console.log("Could not find center row in new data", bookRowData.value);
    return;
  }

  const elmCenterTr = elmScroll.querySelectorAll('tbody tr')[centerRowInd];
  if (!elmCenterTr)
    return;

  // Measure the row's new offset from center
  const tableY = elmTable.getBoundingClientRect().top;
  const rowY = elmCenterTr.getBoundingClientRect().top;

  elmScroll.scrollTop = (rowY - tableY) - rowOffsetFromScrollElm;
}

onMounted(() => {
  scrollBookToCenter();
});
</script>

<style scoped lang="scss">
//Component-specific styles that are unique to MarketOrderInterface

.text-vol {
  color: var(--color-text-muted);
}

.q-table {
  overflow: hidden;
}
</style>
