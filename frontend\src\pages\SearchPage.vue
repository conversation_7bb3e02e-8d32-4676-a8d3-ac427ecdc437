<template>
  <q-page class="row justify-evenly">
    <div class="col-12" style="max-width: 800px;">

      <!-- Loading State (only for initial load, not load more) -->
      <div v-if="searchStore.loading && searchStore.currentPage === 1" class="row justify-center q-pa-xl">
        <q-spinner-dots size="48px" color="primary" />
      </div>

      <!-- Results Table -->
      <div v-else-if="searchStore.searchResults.length > 0" class="search-results">
        <div class="table-container" ref="scrollContainer">
          <q-table
            flat
            :rows="searchResultsProcessed"
            row-key="id"
            hide-pagination
            :pagination="pagination"
            class="search-results-table"
          >
          <template v-slot:header>
            <q-tr>
              <q-th class="event-header" style="width: auto; min-width: 200px; text-align: left;">Event</q-th>
              <q-th class="odds-header" style="width: 150px; text-align: center;">Odds</q-th>
              <q-th class="volume-header" style="width: 120px; text-align: right;">Volume</q-th>
            </q-tr>
          </template>

          <template v-slot:body="props: { row: ProcessedEventData }">
            <q-tr :props="props" class="search-result-row">
              <!-- Event Image + Title Column -->
              <q-td class="event-cell" style="width: auto; min-width: 200px; text-align: left;">
                <router-link
                  :to="{ path: `/events/${props.row.slug}` }"
                  class="event-link"
                >
                  <div class="row items-center no-wrap">
                    <div class="event-image q-mr-md">
                      <img :src="props.row.image" :alt="props.row.title" />
                    </div>
                    <div class="event-title-container">
                      <div class="event-title">
                        {{ props.row.title }}
                        <q-icon v-if="props.row.closed" name="lock" size="12px" class="q-ml-xs text-grey-5" />
                      </div>
                    </div>
                  </div>
                </router-link>
              </q-td>

              <!-- Odds bar column -->
              <q-td class="odds-cell" style="width: 150px; text-align: center;">
                <div v-if="props.row.markets.length > 0" class="odds-container">
                  <div v-for="(market, index) in props.row.markets" :key="market.id" class="market-odds-row">
                    <q-linear-progress color="positive" track-color="black"
                      :class="[
                        'odds-bar',
                        { 'odds-bar-single': props.row.markets.length === 1 },
                        { 'odds-bar-first': index === 0 },
                        { 'odds-bar-middle': index > 0 && index < props.row.markets.length - 1 },
                        { 'odds-bar-last': index === props.row.markets.length - 1 }
                      ]"
                      v-if="!market.resolvedAs"
                      :value="market.outcomeAPrice"
                    >
                      <div class="odds-percentage">
                        <span class="shortname-text">{{ market.shortName }}</span>&nbsp;&nbsp;<span class="percentage-text">{{ Math.round(market.outcomeAPrice * 100) }}%</span>
                      </div>
                    </q-linear-progress>
                    <q-linear-progress v-else
                      :color="getResolvedColor(market.resolvedAs)"
                      :track-color="getResolvedTrackColor(market.resolvedAs)"
                      :class="[
                        'odds-bar',
                        'resolved-bar',
                        { 'odds-bar-single': props.row.markets.length === 1 },
                        { 'odds-bar-first': index === 0 },
                        { 'odds-bar-middle': index > 0 && index < props.row.markets.length - 1 },
                        { 'odds-bar-last': index === props.row.markets.length - 1 }
                      ]"
                      :value="1"
                      stripe
                    >
                      <div class="odds-percentage resolved-text">
                        <strong class="shortname-text">{{ market.resolvedAs }}</strong>
                      </div>
                    </q-linear-progress>
                  </div>
                  <div v-if="props.row.totalMarkets > 0" class="remaining-markets">
                    <strong>+{{ props.row.totalMarkets }} more</strong>
                  </div>
                </div>
                <div v-else class="text-grey-6">No odds</div>
              </q-td>

              <!-- Volume Column -->
              <q-td class="volume-cell" style="width: 120px; text-align: right; font-weight: 500;">
                {{ formatCurrency(props.row.volume) }}
              </q-td>
            </q-tr>
          </template>
          </q-table>

          <!-- Loading more indicator -->
          <div v-if="searchStore.loading && searchStore.currentPage > 1" class="row justify-center q-pa-md">
            <q-spinner-dots size="24px" color="primary" />
            <span class="q-ml-sm text-grey-6">Loading more results...</span>
          </div>

          <!-- End of results indicator -->
          <div v-else-if="searchStore.isLastPage && searchStore.searchResults.length > 0" class="row justify-center q-pa-md">
            <span class="text-grey-6">End of results</span>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else-if="searchStore.searchQuery && searchStore.searchResults.length > 0" class="row justify-center q-pa-xl">
        <div class="text-center">
          <q-icon name="search_off" size="48px" color="grey-5" class="q-mb-md" />
          <div class="text-h6 text-grey-7">No results found</div>
          <div class="text-body2 text-grey-6 q-mt-sm">
            Try adjusting your search terms
          </div>
        </div>
      </div>

      <!-- No Search Query -->
      <div v-else-if="!searchStore.searchQuery && !(searchStore.loading && searchStore.currentPage === 1)" class="row justify-center q-pa-xl">
        <div class="text-center">
          <q-icon name="search" size="48px" color="grey-5" class="q-mb-md" />
          <div class="text-h6 text-grey-7">Enter a search term</div>
          <div class="text-body2 text-grey-6 q-mt-sm">
            Use the search bar above to find events
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAppStore } from 'src/stores/app-store';
import { useSearchStore } from 'src/stores/search-store';
import { PolyGammaEvent } from '@shared/api-dataclasses-shared';
import { filterMap, formatCurrency } from 'src/utils';
import SearchHeaderExtension from 'src/components/headerExtensions/SearchHeaderExtension.vue';

type ProcessedEventData = ReturnType<typeof processEventsData>[0];

const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const searchStore = useSearchStore();

const scrollContainer = ref<HTMLElement | null>(null);
const searchResultsProcessed = ref<ProcessedEventData[]>([]);

const pagination = ref({
  page: 1,
  rowsPerPage: 0, // 0 means show all rows
  sortBy: null,
  descending: false
});

//Functions

function setupHeader() {
  appStore.setHeaderExtensionComp(SearchHeaderExtension);
}

async function doSearch() {
  if (searchStore.searchQuery) {
    await searchStore.searchEvents();
  }
}

function onPageScroll() {
  //Infinite scroll
  const isNearBottom = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 100;

  if (isNearBottom && !searchStore.isLastPage && !searchStore.loading) {
    searchStore.loadNextSearchPage();
  }
}

function getResolvedColor(resolvedAs: string): string {
  if (resolvedAs.toLowerCase().includes('yes')) {
    return 'positive';
  }
  else if (resolvedAs.toLowerCase().includes('no')) {
    return 'negative';
  }
  else {
    return 'grey-5';
  }
}

function getResolvedTrackColor(resolvedAs: string): string {
  if (resolvedAs.toLowerCase().includes('yes')) {
    return 'light-green-2';
  }
  else if (resolvedAs.toLowerCase().includes('no')) {
    return 'red-2';
  }
  else {
    return 'grey-3';
  }
}

/**
 * Turn event search data into view model data.
 * @param events
 */
function processEventsData(events: PolyGammaEvent[]) {
  return events.map(event => {
    //Create market data with odds and short names for active markets
    const marketInfos = filterMap(event.markets, market => {
      if (!event.closed && market.closed)
       return;

      const priceA = parseFloat(market.outcomePrices[0]) || 0;
      const priceB = parseFloat(market.outcomePrices[1]) || 0;
        return {
          id: market.id,
          shortName: event.markets.length == 1 ? market.outcomes[0] : market.groupItemTitle.length > 12 ? market.groupItemTitle.substring(0, 10).trimEnd() + "…" : market.groupItemTitle,
          outcomeAPrice: priceA,
          outcomeBPrice: priceB,
          resolvedAs: market.closed ? `${(event.markets.length == 1 ? "" : market.groupItemTitle)} ${(priceA > priceB ? market.outcomes[0] : market.outcomes[1])}` : undefined,
        };
      })
      .sort((a, b) => b.outcomeAPrice - a.outcomeAPrice); //desc

    const topMarkets = marketInfos.slice(0, 4);
    const totalMarkets = Math.max(0, marketInfos.length - 4);

    return {
      id: event.id,
      slug: event.slug,
      image: event.image,
      title: event.title,
      volume: event.volume,
      closed: event.closed,
      markets: topMarkets,
      totalMarkets
    };
  });
}

//Process search results when they change
watch(() => searchStore.searchResults, (newResults) => {
  searchResultsProcessed.value = processEventsData(newResults);
}, { deep: true });

//Update search when route changes
watch(() => route.query.q, (newQuery) => {
  searchStore.setSearchFilters({ query: newQuery as string });
  doSearch();
});

onMounted(() => {
  setupHeader();
  //Get search query from route
  const queryParam = route.query.q;
  if (typeof queryParam !== 'string')
    throw new Error("Search query cannot be an array");

  searchStore.setSearchFilters({ query: queryParam });
  doSearch();

  //Add page scroll listener for infinite scroll
  window.addEventListener('scroll', onPageScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', onPageScroll);
  appStore.clearHeaderExtensionComp();
});
</script>

<style scoped lang="scss">
.search-header {
  border-bottom: 1px solid var(--color-border-secondary);
}

.search-results {
  min-height: 400px;
  padding-top: 24px;
}

.table-container {
  height: 100%;
  overflow-y: auto;
  width: 100%;
}

.search-results-table {
  .search-result-row {
    height: 80px;
  }

  .event-image {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .event-title-container {
    flex: 1;
    min-width: 0; // Allow text to wrap
  }

  .event-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 2.6em; // 2 lines * 1.3 line-height
  }

  .odds-container {
    display: flex;
    flex-direction: column;
  }

  .remaining-markets {
    margin-top: 0px;
    font-size: var(--font-size-xs);
    color: var(--color-text-muted);
    text-align: center;
  }

  .odds-display {
    justify-content: center;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
  }

  .odds-yes {
    color: var(--color-yes-primary);
    border-radius: var(--radius-sm);
  }

  .odds-no {
    color: var(--color-no-primary);
    border-radius: var(--radius-sm);
  }

  .odds-progress-container {
    position: relative;
    width: 100%;
  }

  .odds-bar {
    height: 14px;
    border-radius: var(--radius-sm);
    position: relative;
  }

  .odds-bar-single {
    height: 28px;
  }

  .odds-bar-first {
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  }

  .odds-bar-middle {
    border-radius: 0;
  }

  .odds-bar-last {
    border-radius: 0 0 var(--radius-sm) var(--radius-sm);
  }

  .odds-bar-first.odds-bar-last {
    border-radius: var(--radius-sm);
  }

  .odds-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    z-index: 1;
  }

  .shortname-text {
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  }

  .percentage-text {
    text-shadow:
      -1px -1px 0 #000,
      1px -1px 0 #000,
      -1px 1px 0 #000,
      1px 1px 0 #000,
      0 1px 2px rgba(0,0,0,0.3);
  }

  .resolved-text {
    color: var(--color-text-primary);
    text-shadow: none;
  }
}

.event-link {
  display: block;
  text-decoration: none;
  color: inherit;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

// Override Quasar table header styles
:deep(.q-table__top) {
  padding: 0;
}

:deep(.q-table) {
  table-layout: fixed;
  width: 100%;
}

:deep(.q-table thead th) {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  border-bottom: 2px solid var(--color-border-secondary);
  padding: var(--spacing-md) var(--spacing-lg);
  padding: 0;
  border: none;
  visibility: hidden;
}

:deep(.q-table thead tr) {
  display: none;
}

:deep(.q-table tbody td) {
  border-bottom: 1px solid var(--color-border-secondary);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

:deep(.q-table tbody tr:hover) {
  background-color: var(--color-surface-hover);
}

// Event title cell - allow text wrapping
:deep(.q-table tbody td:first-child) {
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

// Odds and volume cells - prevent wrapping
:deep(.q-table tbody td:nth-child(2)),
:deep(.q-table tbody td:nth-child(3)) {
  white-space: nowrap;
}
</style>
